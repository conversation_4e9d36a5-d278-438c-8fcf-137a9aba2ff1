const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Admin = require('../models/Admin');

// 验证JWT Token
const verifyToken = (req, res, next) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      success: false,
      message: '访问令牌缺失'
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({
      success: false,
      message: '访问令牌无效或已过期'
    });
  }
};

// 验证用户身份（员工）
const authenticateUser = async (req, res, next) => {
  try {
    if (!req.user || req.user.type !== 'user') {
      return res.status(403).json({
        success: false,
        message: '需要员工身份认证'
      });
    }

    // 验证用户是否仍然存在且有效
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(403).json({
        success: false,
        message: '用户不存在或已被禁用'
      });
    }

    req.currentUser = user;
    next();
  } catch (error) {
    console.error('用户身份验证错误:', error);
    res.status(500).json({
      success: false,
      message: '身份验证失败'
    });
  }
};

// 验证管理员身份
const authenticateAdmin = async (req, res, next) => {
  try {
    if (!req.user || req.user.type !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '需要管理员身份认证'
      });
    }

    // 验证管理员是否仍然存在且有效
    const admin = await Admin.findById(req.user.id);
    if (!admin) {
      return res.status(403).json({
        success: false,
        message: '管理员不存在或已被禁用'
      });
    }

    req.currentAdmin = admin;
    next();
  } catch (error) {
    console.error('管理员身份验证错误:', error);
    res.status(500).json({
      success: false,
      message: '身份验证失败'
    });
  }
};

// 检查管理员权限
const requirePermission = (requiredRole) => {
  return (req, res, next) => {
    if (!req.currentAdmin) {
      return res.status(403).json({
        success: false,
        message: '需要管理员身份'
      });
    }

    if (!Admin.hasPermission(req.currentAdmin, requiredRole)) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    next();
  };
};

// 生成JWT Token
const generateToken = (payload, expiresIn = process.env.JWT_EXPIRES_IN || '24h') => {
  return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn });
};

// 可选的身份验证（允许匿名访问）
const optionalAuth = (req, res, next) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    req.user = null;
    return next();
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
  } catch (error) {
    req.user = null;
  }

  next();
};

module.exports = {
  verifyToken,
  authenticateUser,
  authenticateAdmin,
  requirePermission,
  generateToken,
  optionalAuth
};
