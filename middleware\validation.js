const { body, param, query, validationResult } = require('express-validator');

// 处理验证错误
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '输入数据验证失败',
      errors: errors.array()
    });
  }
  next();
};

// 登录验证规则
const validateLogin = [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空')
    .isLength({ min: 2, max: 50 })
    .withMessage('用户名长度应在2-50个字符之间'),
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符'),
  handleValidationErrors
];

// 积分调整验证规则
const validatePointsAdjustment = [
  body('user_id')
    .isInt({ min: 1 })
    .withMessage('用户ID必须是正整数'),
  body('points_change')
    .isInt()
    .withMessage('积分变动必须是整数')
    .custom((value) => {
      if (value === 0) {
        throw new Error('积分变动不能为0');
      }
      return true;
    }),
  body('reason')
    .notEmpty()
    .withMessage('变动原因不能为空')
    .isLength({ min: 2, max: 500 })
    .withMessage('变动原因长度应在2-500个字符之间'),
  body('type')
    .isIn(['system_award', 'manual_adjust'])
    .withMessage('积分类型无效'),
  handleValidationErrors
];

// 商品创建验证规则
const validateRewardItem = [
  body('name')
    .notEmpty()
    .withMessage('商品名称不能为空')
    .isLength({ min: 1, max: 200 })
    .withMessage('商品名称长度应在1-200个字符之间'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('商品描述长度不能超过1000个字符'),
  body('points_cost')
    .isInt({ min: 1 })
    .withMessage('所需积分必须是正整数'),
  body('stock_quantity')
    .isInt({ min: 0 })
    .withMessage('库存数量必须是非负整数'),
  body('category_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数'),
  handleValidationErrors
];

// 兑换订单创建验证规则
const validateRedemption = [
  body('item_id')
    .isInt({ min: 1 })
    .withMessage('商品ID必须是正整数'),
  handleValidationErrors
];

// 用户创建验证规则
const validateUserCreation = [
  body('employee_id')
    .notEmpty()
    .withMessage('员工工号不能为空')
    .isLength({ min: 1, max: 20 })
    .withMessage('员工工号长度应在1-20个字符之间'),
  body('name')
    .notEmpty()
    .withMessage('员工姓名不能为空')
    .isLength({ min: 1, max: 100 })
    .withMessage('员工姓名长度应在1-100个字符之间'),
  body('password')
    .optional()
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符'),
  body('department_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('部门ID必须是正整数'),
  handleValidationErrors
];

// 分页参数验证
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须是1-100之间的整数'),
  handleValidationErrors
];

// ID参数验证
const validateId = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID必须是正整数'),
  handleValidationErrors
];

// 订单处理验证规则
const validateOrderProcessing = [
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('备注长度不能超过500个字符'),
  handleValidationErrors
];

// 分类创建验证规则
const validateCategory = [
  body('name')
    .notEmpty()
    .withMessage('分类名称不能为空')
    .isLength({ min: 1, max: 100 })
    .withMessage('分类名称长度应在1-100个字符之间'),
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('排序值必须是非负整数'),
  handleValidationErrors
];

// 管理员创建验证规则
const validateAdminCreation = [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度应在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('密码长度至少8个字符')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含至少一个小写字母、一个大写字母和一个数字'),
  body('role_id')
    .isInt({ min: 1 })
    .withMessage('角色ID必须是正整数'),
  handleValidationErrors
];

// 搜索参数验证
const validateSearch = [
  query('search')
    .optional()
    .isLength({ max: 100 })
    .withMessage('搜索关键词长度不能超过100个字符'),
  query('sort_by')
    .optional()
    .isIn(['created_at', 'points_cost', 'name', 'stock_quantity'])
    .withMessage('排序字段无效'),
  query('sort_order')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('排序方向必须是ASC或DESC'),
  handleValidationErrors
];

module.exports = {
  validateLogin,
  validatePointsAdjustment,
  validateRewardItem,
  validateRedemption,
  validateUserCreation,
  validatePagination,
  validateId,
  validateOrderProcessing,
  validateCategory,
  validateAdminCreation,
  validateSearch,
  handleValidationErrors
};
