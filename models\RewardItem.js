const { query } = require('../config/database');

class RewardItem {
  // 获取商品列表
  static async getList(options = {}) {
    const { 
      page = 1, 
      limit = 20, 
      category_id = null, 
      search = '', 
      sort_by = 'created_at', 
      sort_order = 'DESC',
      status = 'active'
    } = options;
    
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE ri.status = ?';
    let params = [status];
    
    if (category_id) {
      whereClause += ' AND ri.category_id = ?';
      params.push(category_id);
    }
    
    if (search) {
      whereClause += ' AND (ri.name LIKE ? OR ri.description LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }
    
    // 验证排序字段
    const allowedSortFields = ['created_at', 'points_cost', 'name', 'stock_quantity'];
    const sortField = allowedSortFields.includes(sort_by) ? sort_by : 'created_at';
    const sortDirection = sort_order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    
    const sql = `
      SELECT ri.*, rc.name as category_name
      FROM reward_items ri
      LEFT JOIN reward_categories rc ON ri.category_id = rc.id
      ${whereClause}
      ORDER BY ri.${sortField} ${sortDirection}
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const items = await query(sql, params);
    
    // 获取总数
    const countSql = `
      SELECT COUNT(*) as total 
      FROM reward_items ri 
      ${whereClause}
    `;
    const countResult = await query(countSql, params.slice(0, -2));
    const total = countResult[0].total;
    
    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 根据ID获取商品详情
  static async findById(id) {
    const sql = `
      SELECT ri.*, rc.name as category_name
      FROM reward_items ri
      LEFT JOIN reward_categories rc ON ri.category_id = rc.id
      WHERE ri.id = ?
    `;
    const items = await query(sql, [id]);
    return items[0] || null;
  }

  // 创建新商品
  static async create(itemData) {
    const { name, description, image_url, category_id, points_cost, stock_quantity } = itemData;
    
    const sql = `
      INSERT INTO reward_items (name, description, image_url, category_id, points_cost, stock_quantity) 
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    const result = await query(sql, [name, description, image_url, category_id, points_cost, stock_quantity]);
    return result.insertId;
  }

  // 更新商品信息
  static async update(id, updateData) {
    const allowedFields = ['name', 'description', 'image_url', 'category_id', 'points_cost', 'stock_quantity', 'status'];
    const fields = [];
    const values = [];
    
    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key) && value !== undefined) {
        fields.push(`${key} = ?`);
        values.push(value);
      }
    }
    
    if (fields.length === 0) {
      throw new Error('没有有效的更新字段');
    }
    
    values.push(id);
    const sql = `UPDATE reward_items SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
    
    const result = await query(sql, values);
    return result.affectedRows > 0;
  }

  // 更新库存（在事务中使用）
  static async updateStock(connection, itemId, newStock) {
    const sql = 'UPDATE reward_items SET stock_quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
    await connection.execute(sql, [newStock, itemId]);
  }

  // 检查库存是否足够
  static async checkStock(itemId, quantity = 1) {
    const sql = 'SELECT stock_quantity FROM reward_items WHERE id = ? AND status = "active"';
    const result = await query(sql, [itemId]);
    
    if (result.length === 0) {
      return { available: false, reason: '商品不存在或已下架' };
    }
    
    const currentStock = result[0].stock_quantity;
    if (currentStock < quantity) {
      return { available: false, reason: '库存不足', currentStock };
    }
    
    return { available: true, currentStock };
  }

  // 删除商品（软删除）
  static async softDelete(id) {
    const sql = "UPDATE reward_items SET status = 'inactive', updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    const result = await query(sql, [id]);
    return result.affectedRows > 0;
  }

  // 获取分类列表
  static async getCategories() {
    const sql = `
      SELECT * FROM reward_categories 
      WHERE status = 'active' 
      ORDER BY sort_order ASC, name ASC
    `;
    return await query(sql);
  }

  // 创建新分类
  static async createCategory(categoryData) {
    const { name, sort_order = 0 } = categoryData;
    
    const sql = 'INSERT INTO reward_categories (name, sort_order) VALUES (?, ?)';
    const result = await query(sql, [name, sort_order]);
    return result.insertId;
  }

  // 更新分类
  static async updateCategory(id, updateData) {
    const allowedFields = ['name', 'sort_order', 'status'];
    const fields = [];
    const values = [];
    
    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key) && value !== undefined) {
        fields.push(`${key} = ?`);
        values.push(value);
      }
    }
    
    if (fields.length === 0) {
      throw new Error('没有有效的更新字段');
    }
    
    values.push(id);
    const sql = `UPDATE reward_categories SET ${fields.join(', ')} WHERE id = ?`;
    
    const result = await query(sql, values);
    return result.affectedRows > 0;
  }

  // 删除分类
  static async deleteCategory(id) {
    // 检查是否有商品使用此分类
    const checkSql = 'SELECT COUNT(*) as count FROM reward_items WHERE category_id = ?';
    const checkResult = await query(checkSql, [id]);
    
    if (checkResult[0].count > 0) {
      throw new Error('该分类下还有商品，无法删除');
    }
    
    const sql = "UPDATE reward_categories SET status = 'inactive' WHERE id = ?";
    const result = await query(sql, [id]);
    return result.affectedRows > 0;
  }
}

module.exports = RewardItem;
