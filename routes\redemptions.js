const express = require('express');
const router = express.Router();
const RedemptionOrder = require('../models/RedemptionOrder');
const { verifyToken, authenticateUser } = require('../middleware/auth');
const { validateRedemption, validatePagination, validateId } = require('../middleware/validation');
const response = require('../utils/response');

// 创建兑换订单
router.post('/', verifyToken, authenticateUser, validateRedemption, async (req, res) => {
  try {
    const userId = req.currentUser.id;
    const { item_id } = req.body;
    
    const orderData = {
      user_id: userId,
      item_id: parseInt(item_id)
    };
    
    const result = await RedemptionOrder.create(orderData);
    
    response.created(res, {
      order_id: result.orderId,
      order_number: result.orderNumber,
      points_spent: result.pointsSpent,
      new_balance: result.newBalance
    }, '兑换订单创建成功');
    
  } catch (error) {
    console.error('创建兑换订单错误:', error);
    
    // 处理业务逻辑错误
    if (error.message.includes('不存在') || error.message.includes('已下架')) {
      return response.notFound(res, error.message);
    } else if (error.message.includes('库存不足') || error.message.includes('积分余额不足')) {
      return response.businessError(res, error.message);
    }
    
    response.serverError(res, '创建兑换订单失败');
  }
});

// 获取我的兑换记录
router.get('/me', verifyToken, authenticateUser, validatePagination, async (req, res) => {
  try {
    const userId = req.currentUser.id;
    const { page = 1, limit = 20, status } = req.query;
    
    const options = {
      page: parseInt(page),
      limit: parseInt(limit)
    };
    
    if (status) {
      // 验证状态值
      const validStatuses = ['pending', 'processed', 'cancelled'];
      if (!validStatuses.includes(status)) {
        return response.validationError(res, null, '无效的订单状态');
      }
      options.status = status;
    }
    
    const result = await RedemptionOrder.getUserOrders(userId, options);
    
    // 格式化返回数据
    const formattedOrders = result.orders.map(order => ({
      id: order.id,
      order_number: order.order_number,
      item_id: order.item_id,
      item_name: order.item_name,
      item_image: order.item_image,
      points_spent: order.points_spent,
      status: order.status,
      created_at: order.created_at,
      processed_at: order.processed_at
    }));
    
    response.paginated(res, formattedOrders, {
      page: result.page,
      limit: result.limit,
      total: result.total,
      totalPages: result.totalPages
    }, '获取兑换记录成功');
    
  } catch (error) {
    console.error('获取兑换记录错误:', error);
    response.serverError(res, '获取兑换记录失败');
  }
});

// 获取单个兑换订单详情
router.get('/:id', verifyToken, authenticateUser, validateId, async (req, res) => {
  try {
    const orderId = parseInt(req.params.id);
    const userId = req.currentUser.id;
    
    const order = await RedemptionOrder.findById(orderId);
    
    if (!order) {
      return response.notFound(res, '兑换订单不存在');
    }
    
    // 检查订单是否属于当前用户
    if (order.user_id !== userId) {
      return response.forbidden(res, '无权访问此订单');
    }
    
    const formattedOrder = {
      id: order.id,
      order_number: order.order_number,
      item_id: order.item_id,
      item_name: order.item_name,
      item_description: order.item_description,
      item_image: order.item_image,
      points_spent: order.points_spent,
      status: order.status,
      created_at: order.created_at,
      processed_at: order.processed_at,
      processed_by_name: order.processed_by_name,
      notes: order.notes
    };
    
    response.success(res, formattedOrder, '获取订单详情成功');
    
  } catch (error) {
    console.error('获取订单详情错误:', error);
    response.serverError(res, '获取订单详情失败');
  }
});

// 取消兑换订单（仅限待处理状态）
router.put('/:id/cancel', verifyToken, authenticateUser, validateId, async (req, res) => {
  try {
    const orderId = parseInt(req.params.id);
    const userId = req.currentUser.id;
    
    // 先获取订单信息
    const order = await RedemptionOrder.findById(orderId);
    
    if (!order) {
      return response.notFound(res, '兑换订单不存在');
    }
    
    // 检查订单是否属于当前用户
    if (order.user_id !== userId) {
      return response.forbidden(res, '无权操作此订单');
    }
    
    // 检查订单状态
    if (order.status !== 'pending') {
      return response.businessError(res, '只能取消待处理的订单');
    }
    
    // 取消订单（这里需要管理员权限，员工不能直接取消）
    // 实际项目中可能需要创建取消申请，由管理员审核
    return response.businessError(res, '订单取消需要联系管理员处理');
    
  } catch (error) {
    console.error('取消订单错误:', error);
    response.serverError(res, '取消订单失败');
  }
});

// 获取兑换统计信息
router.get('/me/stats', verifyToken, authenticateUser, async (req, res) => {
  try {
    const userId = req.currentUser.id;
    const { start_date, end_date } = req.query;
    
    // 获取用户的兑换记录
    const allOrders = await RedemptionOrder.getUserOrders(userId, { limit: 1000 });
    
    // 计算统计数据
    const stats = {
      total_orders: allOrders.total,
      pending_orders: 0,
      processed_orders: 0,
      cancelled_orders: 0,
      total_points_spent: 0,
      recent_orders: allOrders.orders.slice(0, 5).map(order => ({
        id: order.id,
        order_number: order.order_number,
        item_name: order.item_name,
        points_spent: order.points_spent,
        status: order.status,
        created_at: order.created_at
      }))
    };
    
    // 统计各状态订单数量和总消费积分
    allOrders.orders.forEach(order => {
      switch (order.status) {
        case 'pending':
          stats.pending_orders++;
          break;
        case 'processed':
          stats.processed_orders++;
          break;
        case 'cancelled':
          stats.cancelled_orders++;
          break;
      }
      
      if (order.status === 'processed') {
        stats.total_points_spent += order.points_spent;
      }
    });
    
    response.success(res, stats, '获取兑换统计成功');
    
  } catch (error) {
    console.error('获取兑换统计错误:', error);
    response.serverError(res, '获取兑换统计失败');
  }
});

module.exports = router;
