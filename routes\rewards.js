const express = require('express');
const router = express.Router();
const RewardItem = require('../models/RewardItem');
const { optionalAuth } = require('../middleware/auth');
const { validatePagination, validateId, validateSearch } = require('../middleware/validation');
const response = require('../utils/response');

// 获取商品列表（公开接口，支持分页、搜索、筛选）
router.get('/items', optionalAuth, validatePagination, validateSearch, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category_id,
      search = '',
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = req.query;
    
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      search: search.trim(),
      sort_by,
      sort_order: sort_order.toUpperCase(),
      status: 'active' // 只显示上架的商品
    };
    
    if (category_id) {
      options.category_id = parseInt(category_id);
    }
    
    const result = await RewardItem.getList(options);
    
    // 格式化返回数据
    const formattedItems = result.items.map(item => ({
      id: item.id,
      name: item.name,
      description: item.description,
      image_url: item.image_url,
      category_id: item.category_id,
      category_name: item.category_name,
      points_cost: item.points_cost,
      stock_quantity: item.stock_quantity,
      status: item.status,
      created_at: item.created_at
    }));
    
    response.paginated(res, formattedItems, {
      page: result.page,
      limit: result.limit,
      total: result.total,
      totalPages: result.totalPages
    }, '获取商品列表成功');
    
  } catch (error) {
    console.error('获取商品列表错误:', error);
    response.serverError(res, '获取商品列表失败');
  }
});

// 获取单个商品详情
router.get('/items/:id', validateId, async (req, res) => {
  try {
    const itemId = parseInt(req.params.id);
    const item = await RewardItem.findById(itemId);
    
    if (!item) {
      return response.notFound(res, '商品不存在');
    }
    
    // 只返回上架的商品详情（除非是管理员）
    if (item.status !== 'active') {
      return response.notFound(res, '商品不存在或已下架');
    }
    
    const formattedItem = {
      id: item.id,
      name: item.name,
      description: item.description,
      image_url: item.image_url,
      category_id: item.category_id,
      category_name: item.category_name,
      points_cost: item.points_cost,
      stock_quantity: item.stock_quantity,
      status: item.status,
      created_at: item.created_at,
      updated_at: item.updated_at
    };
    
    response.success(res, formattedItem, '获取商品详情成功');
    
  } catch (error) {
    console.error('获取商品详情错误:', error);
    response.serverError(res, '获取商品详情失败');
  }
});

// 获取商品分类列表
router.get('/categories', async (req, res) => {
  try {
    const categories = await RewardItem.getCategories();
    
    const formattedCategories = categories.map(category => ({
      id: category.id,
      name: category.name,
      sort_order: category.sort_order,
      status: category.status
    }));
    
    response.success(res, formattedCategories, '获取分类列表成功');
    
  } catch (error) {
    console.error('获取分类列表错误:', error);
    response.serverError(res, '获取分类列表失败');
  }
});

// 检查商品库存
router.get('/items/:id/stock', validateId, async (req, res) => {
  try {
    const itemId = parseInt(req.params.id);
    const quantity = parseInt(req.query.quantity) || 1;
    
    if (quantity < 1) {
      return response.validationError(res, null, '数量必须大于0');
    }
    
    const stockCheck = await RewardItem.checkStock(itemId, quantity);
    
    if (!stockCheck.available) {
      return response.businessError(res, stockCheck.reason);
    }
    
    response.success(res, {
      available: true,
      current_stock: stockCheck.currentStock,
      requested_quantity: quantity
    }, '库存检查成功');
    
  } catch (error) {
    console.error('检查库存错误:', error);
    response.serverError(res, '检查库存失败');
  }
});

// 获取热门商品（按兑换次数排序）
router.get('/items/popular', validatePagination, async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    
    // 这里需要关联兑换订单表来统计热门商品
    // 暂时返回按创建时间排序的商品
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sort_by: 'created_at',
      sort_order: 'DESC',
      status: 'active'
    };
    
    const result = await RewardItem.getList(options);
    
    const formattedItems = result.items.map(item => ({
      id: item.id,
      name: item.name,
      image_url: item.image_url,
      points_cost: item.points_cost,
      stock_quantity: item.stock_quantity,
      category_name: item.category_name
    }));
    
    response.paginated(res, formattedItems, {
      page: result.page,
      limit: result.limit,
      total: result.total,
      totalPages: result.totalPages
    }, '获取热门商品成功');
    
  } catch (error) {
    console.error('获取热门商品错误:', error);
    response.serverError(res, '获取热门商品失败');
  }
});

// 按分类获取商品
router.get('/categories/:id/items', validateId, validatePagination, async (req, res) => {
  try {
    const categoryId = parseInt(req.params.id);
    const { page = 1, limit = 20, sort_by = 'created_at', sort_order = 'DESC' } = req.query;
    
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      category_id: categoryId,
      sort_by,
      sort_order: sort_order.toUpperCase(),
      status: 'active'
    };
    
    const result = await RewardItem.getList(options);
    
    const formattedItems = result.items.map(item => ({
      id: item.id,
      name: item.name,
      description: item.description,
      image_url: item.image_url,
      points_cost: item.points_cost,
      stock_quantity: item.stock_quantity,
      created_at: item.created_at
    }));
    
    response.paginated(res, formattedItems, {
      page: result.page,
      limit: result.limit,
      total: result.total,
      totalPages: result.totalPages
    }, '获取分类商品成功');
    
  } catch (error) {
    console.error('获取分类商品错误:', error);
    response.serverError(res, '获取分类商品失败');
  }
});

module.exports = router;
