const { pool } = require('../config/database');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// 创建数据库表的SQL语句
const createTables = [
  // 部门表
  `CREATE TABLE IF NOT EXISTS departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    parent_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES departments(id) ON DELETE SET NULL
  )`,

  // 角色表
  `CREATE TABLE IF NOT EXISTS roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )`,

  // 管理员表
  `CREATE TABLE IF NOT EXISTS admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role_id INT NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id)
  )`,

  // 员工表
  `CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255),
    department_id INT,
    current_points INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    INDEX idx_employee_id (employee_id),
    INDEX idx_current_points (current_points)
  )`,

  // 积分流水表
  `CREATE TABLE IF NOT EXISTS points_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    points_change INT NOT NULL,
    balance_after INT NOT NULL,
    reason VARCHAR(500) NOT NULL,
    type ENUM('system_award', 'manual_adjust', 'redemption', 'refund') NOT NULL,
    operator_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (operator_id) REFERENCES admins(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_type (type)
  )`,

  // 奖励物品分类表
  `CREATE TABLE IF NOT EXISTS reward_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    sort_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )`,

  // 奖励物品表
  `CREATE TABLE IF NOT EXISTS reward_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    image_url VARCHAR(500),
    category_id INT,
    points_cost INT NOT NULL,
    stock_quantity INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES reward_categories(id) ON DELETE SET NULL,
    INDEX idx_category_id (category_id),
    INDEX idx_points_cost (points_cost),
    INDEX idx_status (status)
  )`,

  // 兑换订单表
  `CREATE TABLE IF NOT EXISTS redemption_orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    user_id INT NOT NULL,
    item_id INT NOT NULL,
    points_spent INT NOT NULL,
    status ENUM('pending', 'processed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    processed_by INT,
    notes TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES reward_items(id),
    FOREIGN KEY (processed_by) REFERENCES admins(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
  )`
];

// 初始化数据
const initData = async () => {
  try {
    // 插入默认角色
    await pool.execute(`
      INSERT IGNORE INTO roles (role_name, description) VALUES 
      ('super_admin', '超级管理员'),
      ('admin', '普通管理员')
    `);

    // 插入默认部门
    await pool.execute(`
      INSERT IGNORE INTO departments (name) VALUES 
      ('人事部'),
      ('技术部'),
      ('市场部'),
      ('财务部')
    `);

    // 插入默认管理员账户
    const hashedPassword = await bcrypt.hash(process.env.DEFAULT_ADMIN_PASSWORD || 'admin123', 10);
    await pool.execute(`
      INSERT IGNORE INTO admins (username, password_hash, role_id) 
      SELECT ?, ?, id FROM roles WHERE role_name = 'super_admin'
    `, [process.env.DEFAULT_ADMIN_USERNAME || 'admin', hashedPassword]);

    // 插入默认奖励分类
    await pool.execute(`
      INSERT IGNORE INTO reward_categories (name, sort_order) VALUES 
      ('生活用品', 1),
      ('生活福利', 2),
      ('特殊奖励', 3)
    `);

    console.log('✅ 初始数据插入完成');
  } catch (error) {
    console.error('❌ 初始数据插入失败:', error);
    throw error;
  }
};

// 执行迁移
const migrate = async () => {
  try {
    console.log('🚀 开始数据库迁移...');
    
    for (const sql of createTables) {
      await pool.execute(sql);
    }
    
    console.log('✅ 数据表创建完成');
    
    await initData();
    
    console.log('🎉 数据库迁移完成！');
    process.exit(0);
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error);
    process.exit(1);
  }
};

// 如果直接运行此文件，则执行迁移
if (require.main === module) {
  migrate();
}

module.exports = { migrate };
