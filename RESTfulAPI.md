*   **认证接口 (`/auth`)**
    *   `POST /auth/login`: 管理员或员工登录，返回一个 Token。
*   **用户接口 (`/users`)**
    *   `GET /users/me`: 获取当前登录员工的信息（包括积分）。
    *   `GET /users/me/points-log`: 获取当前员工的积分流水。
*   **商品接口 (`/rewards`)**
    *   `GET /rewards/items`: 获取商品列表（支持分页）。
    *   `GET /rewards/items/{id}`: 获取单个商品详情。
*   **兑换接口 (`/redemptions`)**
    *   `POST /redemptions`: 创建一个新的兑换订单。
    *   `GET /users/me/redemptions`: 获取我的兑换记录。
*   **后台管理接口 (`/admin`) - 需要管理员权限**
    *   `GET /admin/users`: 搜索/获取员工列表。
    *   `POST /admin/points/adjust`: 为员工调整积分。
    *   `POST /admin/rewards/items`: 新增一个商品。
    *   `PUT /admin/rewards/items/{id}`: 修改一个商品。
    *   `GET /admin/redemptions`: 获取所有兑换订单列表。
    *   `PUT /admin/redemptions/{id}/process`: 处理一个订单。
