const { query, transaction } = require('../config/database');
const PointsLog = require('./PointsLog');
const RewardItem = require('./RewardItem');

class RedemptionOrder {
  // 生成订单号
  static generateOrderNumber() {
    const now = new Date();
    const timestamp = now.getTime().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `RD${timestamp}${random}`;
  }

  // 创建兑换订单
  static async create(orderData) {
    const { user_id, item_id } = orderData;
    
    return await transaction(async (connection) => {
      // 1. 获取商品信息
      const [itemRows] = await connection.execute(
        'SELECT * FROM reward_items WHERE id = ? AND status = "active"',
        [item_id]
      );
      
      if (itemRows.length === 0) {
        throw new Error('商品不存在或已下架');
      }
      
      const item = itemRows[0];
      
      // 2. 检查库存
      if (item.stock_quantity < 1) {
        throw new Error('商品库存不足');
      }
      
      // 3. 获取用户当前积分
      const [userRows] = await connection.execute(
        'SELECT current_points FROM users WHERE id = ? AND status = "active"',
        [user_id]
      );
      
      if (userRows.length === 0) {
        throw new Error('用户不存在或已禁用');
      }
      
      const currentPoints = userRows[0].current_points;
      
      // 4. 检查积分是否足够
      if (currentPoints < item.points_cost) {
        throw new Error('积分余额不足');
      }
      
      // 5. 生成订单号
      const orderNumber = this.generateOrderNumber();
      
      // 6. 创建订单
      const [orderResult] = await connection.execute(`
        INSERT INTO redemption_orders (order_number, user_id, item_id, points_spent, status) 
        VALUES (?, ?, ?, ?, 'pending')
      `, [orderNumber, user_id, item_id, item.points_cost]);
      
      const orderId = orderResult.insertId;
      
      // 7. 扣除用户积分
      const newBalance = currentPoints - item.points_cost;
      await connection.execute(
        'UPDATE users SET current_points = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [newBalance, user_id]
      );
      
      // 8. 记录积分流水
      await PointsLog.create(connection, {
        user_id,
        points_change: -item.points_cost,
        balance_after: newBalance,
        reason: `兑换商品：${item.name}`,
        type: 'redemption',
        operator_id: null
      });
      
      // 9. 减少商品库存
      await RewardItem.updateStock(connection, item_id, item.stock_quantity - 1);
      
      return {
        orderId,
        orderNumber,
        pointsSpent: item.points_cost,
        newBalance
      };
    });
  }

  // 获取订单详情
  static async findById(id) {
    const sql = `
      SELECT ro.*, ri.name as item_name, ri.description as item_description, 
             ri.image_url as item_image, u.name as user_name, u.employee_id,
             a.username as processed_by_name
      FROM redemption_orders ro
      LEFT JOIN reward_items ri ON ro.item_id = ri.id
      LEFT JOIN users u ON ro.user_id = u.id
      LEFT JOIN admins a ON ro.processed_by = a.id
      WHERE ro.id = ?
    `;
    const orders = await query(sql, [id]);
    return orders[0] || null;
  }

  // 获取用户的兑换记录
  static async getUserOrders(userId, options = {}) {
    const { page = 1, limit = 20, status = null } = options;
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE ro.user_id = ?';
    let params = [userId];
    
    if (status) {
      whereClause += ' AND ro.status = ?';
      params.push(status);
    }
    
    const sql = `
      SELECT ro.*, ri.name as item_name, ri.image_url as item_image
      FROM redemption_orders ro
      LEFT JOIN reward_items ri ON ro.item_id = ri.id
      ${whereClause}
      ORDER BY ro.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const orders = await query(sql, params);
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM redemption_orders ro ${whereClause}`;
    const countResult = await query(countSql, params.slice(0, -2));
    const total = countResult[0].total;
    
    return {
      orders,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 获取所有订单（管理员使用）
  static async getAllOrders(options = {}) {
    const { 
      page = 1, 
      limit = 20, 
      status = null, 
      user_id = null, 
      start_date = null, 
      end_date = null 
    } = options;
    
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE 1=1';
    let params = [];
    
    if (status) {
      whereClause += ' AND ro.status = ?';
      params.push(status);
    }
    
    if (user_id) {
      whereClause += ' AND ro.user_id = ?';
      params.push(user_id);
    }
    
    if (start_date) {
      whereClause += ' AND ro.created_at >= ?';
      params.push(start_date);
    }
    
    if (end_date) {
      whereClause += ' AND ro.created_at <= ?';
      params.push(end_date);
    }
    
    const sql = `
      SELECT ro.*, ri.name as item_name, ri.image_url as item_image,
             u.name as user_name, u.employee_id,
             a.username as processed_by_name
      FROM redemption_orders ro
      LEFT JOIN reward_items ri ON ro.item_id = ri.id
      LEFT JOIN users u ON ro.user_id = u.id
      LEFT JOIN admins a ON ro.processed_by = a.id
      ${whereClause}
      ORDER BY ro.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const orders = await query(sql, params);
    
    // 获取总数
    const countSql = `
      SELECT COUNT(*) as total 
      FROM redemption_orders ro
      LEFT JOIN users u ON ro.user_id = u.id
      ${whereClause}
    `;
    const countResult = await query(countSql, params.slice(0, -2));
    const total = countResult[0].total;
    
    return {
      orders,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 处理订单
  static async processOrder(orderId, adminId, notes = '') {
    const sql = `
      UPDATE redemption_orders 
      SET status = 'processed', processed_at = CURRENT_TIMESTAMP, processed_by = ?, notes = ?
      WHERE id = ? AND status = 'pending'
    `;
    
    const result = await query(sql, [adminId, notes, orderId]);
    return result.affectedRows > 0;
  }

  // 取消订单（退还积分）
  static async cancelOrder(orderId, adminId, reason = '') {
    return await transaction(async (connection) => {
      // 1. 获取订单信息
      const [orderRows] = await connection.execute(
        'SELECT * FROM redemption_orders WHERE id = ? AND status = "pending"',
        [orderId]
      );
      
      if (orderRows.length === 0) {
        throw new Error('订单不存在或已处理');
      }
      
      const order = orderRows[0];
      
      // 2. 获取用户当前积分
      const [userRows] = await connection.execute(
        'SELECT current_points FROM users WHERE id = ?',
        [order.user_id]
      );
      
      const currentPoints = userRows[0].current_points;
      const newBalance = currentPoints + order.points_spent;
      
      // 3. 退还积分
      await connection.execute(
        'UPDATE users SET current_points = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [newBalance, order.user_id]
      );
      
      // 4. 记录积分流水
      await PointsLog.create(connection, {
        user_id: order.user_id,
        points_change: order.points_spent,
        balance_after: newBalance,
        reason: `订单取消退款：${reason}`,
        type: 'refund',
        operator_id: adminId
      });
      
      // 5. 恢复商品库存
      await connection.execute(
        'UPDATE reward_items SET stock_quantity = stock_quantity + 1 WHERE id = ?',
        [order.item_id]
      );
      
      // 6. 更新订单状态
      await connection.execute(
        'UPDATE redemption_orders SET status = "cancelled", processed_at = CURRENT_TIMESTAMP, processed_by = ?, notes = ? WHERE id = ?',
        [adminId, reason, orderId]
      );
      
      return {
        refundedPoints: order.points_spent,
        newBalance
      };
    });
  }

  // 获取订单统计
  static async getStatistics(options = {}) {
    const { start_date = null, end_date = null } = options;
    
    let whereClause = 'WHERE 1=1';
    let params = [];
    
    if (start_date) {
      whereClause += ' AND created_at >= ?';
      params.push(start_date);
    }
    
    if (end_date) {
      whereClause += ' AND created_at <= ?';
      params.push(end_date);
    }
    
    const sql = `
      SELECT 
        status,
        COUNT(*) as count,
        SUM(points_spent) as total_points
      FROM redemption_orders 
      ${whereClause}
      GROUP BY status
    `;
    
    const stats = await query(sql, params);
    return stats;
  }
}

module.exports = RedemptionOrder;
