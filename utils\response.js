// 统一的API响应格式

// 成功响应
const success = (res, data = null, message = '操作成功', statusCode = 200) => {
  return res.status(statusCode).json({
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  });
};

// 错误响应
const error = (res, message = '操作失败', statusCode = 400, errors = null) => {
  return res.status(statusCode).json({
    success: false,
    message,
    errors,
    timestamp: new Date().toISOString()
  });
};

// 分页响应
const paginated = (res, data, pagination, message = '获取成功') => {
  return res.status(200).json({
    success: true,
    message,
    data,
    pagination: {
      page: pagination.page,
      limit: pagination.limit,
      total: pagination.total,
      totalPages: pagination.totalPages
    },
    timestamp: new Date().toISOString()
  });
};

// 创建成功响应
const created = (res, data = null, message = '创建成功') => {
  return success(res, data, message, 201);
};

// 无内容响应
const noContent = (res, message = '操作成功') => {
  return res.status(204).json({
    success: true,
    message,
    timestamp: new Date().toISOString()
  });
};

// 未找到响应
const notFound = (res, message = '资源未找到') => {
  return error(res, message, 404);
};

// 未授权响应
const unauthorized = (res, message = '未授权访问') => {
  return error(res, message, 401);
};

// 禁止访问响应
const forbidden = (res, message = '禁止访问') => {
  return error(res, message, 403);
};

// 服务器错误响应
const serverError = (res, message = '服务器内部错误') => {
  return error(res, message, 500);
};

// 参数验证错误响应
const validationError = (res, errors, message = '参数验证失败') => {
  return error(res, message, 400, errors);
};

// 冲突响应（如重复数据）
const conflict = (res, message = '数据冲突') => {
  return error(res, message, 409);
};

// 业务逻辑错误响应
const businessError = (res, message = '业务逻辑错误') => {
  return error(res, message, 422);
};

module.exports = {
  success,
  error,
  paginated,
  created,
  noContent,
  notFound,
  unauthorized,
  forbidden,
  serverError,
  validationError,
  conflict,
  businessError
};
