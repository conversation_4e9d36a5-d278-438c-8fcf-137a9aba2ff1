const { query, transaction } = require('../config/database');
const bcrypt = require('bcryptjs');

class User {
  // 根据员工ID查找用户
  static async findByEmployeeId(employeeId) {
    const sql = `
      SELECT u.*, d.name as department_name 
      FROM users u 
      LEFT JOIN departments d ON u.department_id = d.id 
      WHERE u.employee_id = ? AND u.status = 'active'
    `;
    const users = await query(sql, [employeeId]);
    return users[0] || null;
  }

  // 根据ID查找用户
  static async findById(id) {
    const sql = `
      SELECT u.*, d.name as department_name 
      FROM users u 
      LEFT JOIN departments d ON u.department_id = d.id 
      WHERE u.id = ? AND u.status = 'active'
    `;
    const users = await query(sql, [id]);
    return users[0] || null;
  }

  // 创建新用户
  static async create(userData) {
    const { employee_id, name, password, department_id } = userData;
    let password_hash = null;
    
    if (password) {
      password_hash = await bcrypt.hash(password, 10);
    }

    const sql = `
      INSERT INTO users (employee_id, name, password_hash, department_id, current_points) 
      VALUES (?, ?, ?, ?, 0)
    `;
    const result = await query(sql, [employee_id, name, password_hash, department_id]);
    return result.insertId;
  }

  // 更新用户积分（在事务中使用）
  static async updatePoints(connection, userId, newPoints) {
    const sql = 'UPDATE users SET current_points = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
    await connection.execute(sql, [newPoints, userId]);
  }

  // 获取用户列表（管理员使用）
  static async getList(options = {}) {
    const { page = 1, limit = 20, search = '', department_id = null } = options;
    const offset = (page - 1) * limit;
    
    let whereClause = "WHERE u.status = 'active'";
    let params = [];
    
    if (search) {
      whereClause += " AND (u.name LIKE ? OR u.employee_id LIKE ?)";
      params.push(`%${search}%`, `%${search}%`);
    }
    
    if (department_id) {
      whereClause += " AND u.department_id = ?";
      params.push(department_id);
    }

    const sql = `
      SELECT u.id, u.employee_id, u.name, u.current_points, u.created_at,
             d.name as department_name
      FROM users u 
      LEFT JOIN departments d ON u.department_id = d.id 
      ${whereClause}
      ORDER BY u.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const users = await query(sql, params);

    // 获取总数
    const countSql = `
      SELECT COUNT(*) as total 
      FROM users u 
      ${whereClause}
    `;
    const countResult = await query(countSql, params.slice(0, -2));
    const total = countResult[0].total;

    return {
      users,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 验证密码
  static async verifyPassword(user, password) {
    if (!user.password_hash || !password) {
      return false;
    }
    return await bcrypt.compare(password, user.password_hash);
  }

  // 更新用户信息
  static async update(id, updateData) {
    const allowedFields = ['name', 'department_id'];
    const fields = [];
    const values = [];
    
    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key) && value !== undefined) {
        fields.push(`${key} = ?`);
        values.push(value);
      }
    }
    
    if (fields.length === 0) {
      throw new Error('没有有效的更新字段');
    }
    
    values.push(id);
    const sql = `UPDATE users SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
    
    const result = await query(sql, values);
    return result.affectedRows > 0;
  }

  // 软删除用户
  static async softDelete(id) {
    const sql = "UPDATE users SET status = 'inactive', updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    const result = await query(sql, [id]);
    return result.affectedRows > 0;
  }
}

module.exports = User;
