const { query, transaction } = require('../config/database');
const User = require('./User');

class PointsLog {
  // 创建积分记录（在事务中）
  static async create(connection, logData) {
    const { user_id, points_change, balance_after, reason, type, operator_id } = logData;
    
    const sql = `
      INSERT INTO points_logs (user_id, points_change, balance_after, reason, type, operator_id) 
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    const result = await connection.execute(sql, [
      user_id, points_change, balance_after, reason, type, operator_id
    ]);
    
    return result[0].insertId;
  }

  // 调整用户积分（完整的事务操作）
  static async adjustUserPoints(adjustData) {
    const { user_id, points_change, reason, type, operator_id } = adjustData;
    
    return await transaction(async (connection) => {
      // 1. 获取用户当前积分
      const [userRows] = await connection.execute(
        'SELECT current_points FROM users WHERE id = ? AND status = "active"',
        [user_id]
      );
      
      if (userRows.length === 0) {
        throw new Error('用户不存在或已禁用');
      }
      
      const currentPoints = userRows[0].current_points;
      const newBalance = currentPoints + points_change;
      
      // 2. 检查积分是否足够（如果是扣分操作）
      if (newBalance < 0) {
        throw new Error('积分余额不足');
      }
      
      // 3. 更新用户积分
      await User.updatePoints(connection, user_id, newBalance);
      
      // 4. 创建积分记录
      const logId = await this.create(connection, {
        user_id,
        points_change,
        balance_after: newBalance,
        reason,
        type,
        operator_id
      });
      
      return {
        logId,
        previousBalance: currentPoints,
        newBalance,
        pointsChange: points_change
      };
    });
  }

  // 获取用户积分流水
  static async getUserLogs(userId, options = {}) {
    const { page = 1, limit = 20, type = null } = options;
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE pl.user_id = ?';
    let params = [userId];
    
    if (type) {
      whereClause += ' AND pl.type = ?';
      params.push(type);
    }
    
    const sql = `
      SELECT pl.*, a.username as operator_name
      FROM points_logs pl
      LEFT JOIN admins a ON pl.operator_id = a.id
      ${whereClause}
      ORDER BY pl.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const logs = await query(sql, params);
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM points_logs pl ${whereClause}`;
    const countResult = await query(countSql, params.slice(0, -2));
    const total = countResult[0].total;
    
    return {
      logs,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 获取所有积分流水（管理员使用）
  static async getAllLogs(options = {}) {
    const { page = 1, limit = 20, user_id = null, type = null, start_date = null, end_date = null } = options;
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE 1=1';
    let params = [];
    
    if (user_id) {
      whereClause += ' AND pl.user_id = ?';
      params.push(user_id);
    }
    
    if (type) {
      whereClause += ' AND pl.type = ?';
      params.push(type);
    }
    
    if (start_date) {
      whereClause += ' AND pl.created_at >= ?';
      params.push(start_date);
    }
    
    if (end_date) {
      whereClause += ' AND pl.created_at <= ?';
      params.push(end_date);
    }
    
    const sql = `
      SELECT pl.*, u.name as user_name, u.employee_id, a.username as operator_name
      FROM points_logs pl
      LEFT JOIN users u ON pl.user_id = u.id
      LEFT JOIN admins a ON pl.operator_id = a.id
      ${whereClause}
      ORDER BY pl.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const logs = await query(sql, params);
    
    // 获取总数
    const countSql = `
      SELECT COUNT(*) as total 
      FROM points_logs pl
      LEFT JOIN users u ON pl.user_id = u.id
      ${whereClause}
    `;
    const countResult = await query(countSql, params.slice(0, -2));
    const total = countResult[0].total;
    
    return {
      logs,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 获取积分统计信息
  static async getStatistics(options = {}) {
    const { start_date = null, end_date = null } = options;
    
    let whereClause = 'WHERE 1=1';
    let params = [];
    
    if (start_date) {
      whereClause += ' AND created_at >= ?';
      params.push(start_date);
    }
    
    if (end_date) {
      whereClause += ' AND created_at <= ?';
      params.push(end_date);
    }
    
    const sql = `
      SELECT 
        type,
        COUNT(*) as count,
        SUM(CASE WHEN points_change > 0 THEN points_change ELSE 0 END) as total_added,
        SUM(CASE WHEN points_change < 0 THEN ABS(points_change) ELSE 0 END) as total_deducted
      FROM points_logs 
      ${whereClause}
      GROUP BY type
    `;
    
    const stats = await query(sql, params);
    
    return stats;
  }
}

module.exports = PointsLog;
