const { query } = require('../config/database');
const bcrypt = require('bcryptjs');

class Admin {
  // 根据用户名查找管理员
  static async findByUsername(username) {
    const sql = `
      SELECT a.*, r.role_name, r.description as role_description
      FROM admins a
      LEFT JOIN roles r ON a.role_id = r.id
      WHERE a.username = ? AND a.status = 'active'
    `;
    const admins = await query(sql, [username]);
    return admins[0] || null;
  }

  // 根据ID查找管理员
  static async findById(id) {
    const sql = `
      SELECT a.*, r.role_name, r.description as role_description
      FROM admins a
      LEFT JOIN roles r ON a.role_id = r.id
      WHERE a.id = ? AND a.status = 'active'
    `;
    const admins = await query(sql, [id]);
    return admins[0] || null;
  }

  // 验证密码
  static async verifyPassword(admin, password) {
    return await bcrypt.compare(password, admin.password_hash);
  }

  // 创建新管理员
  static async create(adminData) {
    const { username, password, role_id } = adminData;
    const password_hash = await bcrypt.hash(password, 10);
    
    const sql = `
      INSERT INTO admins (username, password_hash, role_id) 
      VALUES (?, ?, ?)
    `;
    
    const result = await query(sql, [username, password_hash, role_id]);
    return result.insertId;
  }

  // 更新管理员信息
  static async update(id, updateData) {
    const allowedFields = ['username', 'role_id', 'status'];
    const fields = [];
    const values = [];
    
    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key) && value !== undefined) {
        fields.push(`${key} = ?`);
        values.push(value);
      }
    }
    
    // 如果包含密码更新
    if (updateData.password) {
      const password_hash = await bcrypt.hash(updateData.password, 10);
      fields.push('password_hash = ?');
      values.push(password_hash);
    }
    
    if (fields.length === 0) {
      throw new Error('没有有效的更新字段');
    }
    
    values.push(id);
    const sql = `UPDATE admins SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
    
    const result = await query(sql, values);
    return result.affectedRows > 0;
  }

  // 获取管理员列表
  static async getList(options = {}) {
    const { page = 1, limit = 20, role_id = null } = options;
    const offset = (page - 1) * limit;
    
    let whereClause = "WHERE a.status = 'active'";
    let params = [];
    
    if (role_id) {
      whereClause += ' AND a.role_id = ?';
      params.push(role_id);
    }
    
    const sql = `
      SELECT a.id, a.username, a.status, a.created_at,
             r.role_name, r.description as role_description
      FROM admins a
      LEFT JOIN roles r ON a.role_id = r.id
      ${whereClause}
      ORDER BY a.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const admins = await query(sql, params);
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM admins a ${whereClause}`;
    const countResult = await query(countSql, params.slice(0, -2));
    const total = countResult[0].total;
    
    return {
      admins,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 软删除管理员
  static async softDelete(id) {
    const sql = "UPDATE admins SET status = 'inactive', updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    const result = await query(sql, [id]);
    return result.affectedRows > 0;
  }

  // 获取所有角色
  static async getRoles() {
    const sql = 'SELECT * FROM roles ORDER BY id ASC';
    return await query(sql);
  }

  // 创建新角色
  static async createRole(roleData) {
    const { role_name, description } = roleData;
    
    const sql = 'INSERT INTO roles (role_name, description) VALUES (?, ?)';
    const result = await query(sql, [role_name, description]);
    return result.insertId;
  }

  // 更新角色
  static async updateRole(id, updateData) {
    const allowedFields = ['role_name', 'description'];
    const fields = [];
    const values = [];
    
    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key) && value !== undefined) {
        fields.push(`${key} = ?`);
        values.push(value);
      }
    }
    
    if (fields.length === 0) {
      throw new Error('没有有效的更新字段');
    }
    
    values.push(id);
    const sql = `UPDATE roles SET ${fields.join(', ')} WHERE id = ?`;
    
    const result = await query(sql, values);
    return result.affectedRows > 0;
  }

  // 检查管理员权限
  static hasPermission(admin, requiredRole) {
    const roleHierarchy = {
      'super_admin': 3,
      'admin': 2,
      'viewer': 1
    };
    
    const adminLevel = roleHierarchy[admin.role_name] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;
    
    return adminLevel >= requiredLevel;
  }

  // 记录管理员操作日志
  static async logOperation(adminId, operation, details = '') {
    const sql = `
      INSERT INTO admin_operation_logs (admin_id, operation, details, created_at) 
      VALUES (?, ?, ?, CURRENT_TIMESTAMP)
    `;
    
    try {
      await query(sql, [adminId, operation, details]);
    } catch (error) {
      // 如果日志表不存在，忽略错误（可选功能）
      console.warn('管理员操作日志记录失败:', error.message);
    }
  }
}

module.exports = Admin;
