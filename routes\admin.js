const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Admin = require('../models/Admin');
const PointsLog = require('../models/PointsLog');
const RewardItem = require('../models/RewardItem');
const RedemptionOrder = require('../models/RedemptionOrder');
const { verifyToken, authenticateAdmin, requirePermission } = require('../middleware/auth');
const { 
  validatePointsAdjustment, 
  validateRewardItem, 
  validatePagination, 
  validateId,
  validateOrderProcessing,
  validateCategory,
  validateUserCreation,
  validateAdminCreation,
  validateSearch
} = require('../middleware/validation');
const { uploadSingle } = require('../utils/upload');
const response = require('../utils/response');

// 应用认证中间件到所有管理员路由
router.use(verifyToken, authenticateAdmin);

// ==================== 用户管理 ====================

// 获取员工列表
router.get('/users', validatePagination, validateSearch, async (req, res) => {
  try {
    const { page = 1, limit = 20, search = '', department_id } = req.query;
    
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      search: search.trim()
    };
    
    if (department_id) {
      options.department_id = parseInt(department_id);
    }
    
    const result = await User.getList(options);
    
    response.paginated(res, result.users, {
      page: result.page,
      limit: result.limit,
      total: result.total,
      totalPages: result.totalPages
    }, '获取员工列表成功');
    
  } catch (error) {
    console.error('获取员工列表错误:', error);
    response.serverError(res, '获取员工列表失败');
  }
});

// 获取单个员工详情
router.get('/users/:id', validateId, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const user = await User.findById(userId);
    
    if (!user) {
      return response.notFound(res, '员工不存在');
    }
    
    response.success(res, user, '获取员工详情成功');
    
  } catch (error) {
    console.error('获取员工详情错误:', error);
    response.serverError(res, '获取员工详情失败');
  }
});

// 创建新员工
router.post('/users', requirePermission('admin'), validateUserCreation, async (req, res) => {
  try {
    const { employee_id, name, password, department_id } = req.body;
    
    // 检查员工工号是否已存在
    const existingUser = await User.findByEmployeeId(employee_id);
    if (existingUser) {
      return response.conflict(res, '员工工号已存在');
    }
    
    const userData = {
      employee_id,
      name,
      password,
      department_id: department_id ? parseInt(department_id) : null
    };
    
    const userId = await User.create(userData);
    
    // 记录操作日志
    await Admin.logOperation(
      req.currentAdmin.id, 
      'create_user', 
      `创建员工：${name} (${employee_id})`
    );
    
    response.created(res, { id: userId }, '员工创建成功');
    
  } catch (error) {
    console.error('创建员工错误:', error);
    response.serverError(res, '创建员工失败');
  }
});

// 更新员工信息
router.put('/users/:id', validateId, requirePermission('admin'), async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const { name, department_id } = req.body;
    
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (department_id !== undefined) updateData.department_id = department_id;
    
    if (Object.keys(updateData).length === 0) {
      return response.validationError(res, null, '没有有效的更新字段');
    }
    
    const success = await User.update(userId, updateData);
    
    if (success) {
      // 记录操作日志
      await Admin.logOperation(
        req.currentAdmin.id, 
        'update_user', 
        `更新员工信息：用户ID ${userId}`
      );
      
      response.success(res, null, '员工信息更新成功');
    } else {
      response.notFound(res, '员工不存在');
    }
    
  } catch (error) {
    console.error('更新员工信息错误:', error);
    response.serverError(res, '更新员工信息失败');
  }
});

// ==================== 积分管理 ====================

// 调整员工积分
router.post('/points/adjust', requirePermission('admin'), validatePointsAdjustment, async (req, res) => {
  try {
    const { user_id, points_change, reason, type } = req.body;
    
    const adjustData = {
      user_id: parseInt(user_id),
      points_change: parseInt(points_change),
      reason,
      type,
      operator_id: req.currentAdmin.id
    };
    
    const result = await PointsLog.adjustUserPoints(adjustData);
    
    // 记录操作日志
    await Admin.logOperation(
      req.currentAdmin.id, 
      'adjust_points', 
      `调整积分：用户ID ${user_id}，变动 ${points_change}，原因：${reason}`
    );
    
    response.success(res, {
      previous_balance: result.previousBalance,
      new_balance: result.newBalance,
      points_change: result.pointsChange
    }, '积分调整成功');
    
  } catch (error) {
    console.error('积分调整错误:', error);
    
    if (error.message.includes('不存在') || error.message.includes('已禁用')) {
      return response.notFound(res, error.message);
    } else if (error.message.includes('积分余额不足')) {
      return response.businessError(res, error.message);
    }
    
    response.serverError(res, '积分调整失败');
  }
});

// 获取积分流水记录（管理员视图）
router.get('/points/logs', validatePagination, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      user_id, 
      type, 
      start_date, 
      end_date 
    } = req.query;
    
    const options = {
      page: parseInt(page),
      limit: parseInt(limit)
    };
    
    if (user_id) options.user_id = parseInt(user_id);
    if (type) options.type = type;
    if (start_date) options.start_date = start_date;
    if (end_date) options.end_date = end_date;
    
    const result = await PointsLog.getAllLogs(options);
    
    response.paginated(res, result.logs, {
      page: result.page,
      limit: result.limit,
      total: result.total,
      totalPages: result.totalPages
    }, '获取积分流水成功');
    
  } catch (error) {
    console.error('获取积分流水错误:', error);
    response.serverError(res, '获取积分流水失败');
  }
});

// 获取积分统计
router.get('/points/statistics', async (req, res) => {
  try {
    const { start_date, end_date } = req.query;
    
    const options = {};
    if (start_date) options.start_date = start_date;
    if (end_date) options.end_date = end_date;
    
    const stats = await PointsLog.getStatistics(options);
    
    response.success(res, stats, '获取积分统计成功');
    
  } catch (error) {
    console.error('获取积分统计错误:', error);
    response.serverError(res, '获取积分统计失败');
  }
});

// ==================== 商品管理 ====================

// 获取商品列表（管理员视图，包含下架商品）
router.get('/rewards/items', validatePagination, validateSearch, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category_id,
      search = '',
      sort_by = 'created_at',
      sort_order = 'DESC',
      status
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      search: search.trim(),
      sort_by,
      sort_order: sort_order.toUpperCase()
    };

    if (category_id) options.category_id = parseInt(category_id);
    if (status) options.status = status;

    const result = await RewardItem.getList(options);

    response.paginated(res, result.items, {
      page: result.page,
      limit: result.limit,
      total: result.total,
      totalPages: result.totalPages
    }, '获取商品列表成功');

  } catch (error) {
    console.error('获取商品列表错误:', error);
    response.serverError(res, '获取商品列表失败');
  }
});

// 创建新商品
router.post('/rewards/items', requirePermission('admin'), uploadSingle('image'), validateRewardItem, async (req, res) => {
  try {
    const { name, description, category_id, points_cost, stock_quantity } = req.body;

    const itemData = {
      name,
      description,
      image_url: req.file ? req.file.url : null,
      category_id: category_id ? parseInt(category_id) : null,
      points_cost: parseInt(points_cost),
      stock_quantity: parseInt(stock_quantity)
    };

    const itemId = await RewardItem.create(itemData);

    // 记录操作日志
    await Admin.logOperation(
      req.currentAdmin.id,
      'create_item',
      `创建商品：${name}`
    );

    response.created(res, { id: itemId }, '商品创建成功');

  } catch (error) {
    console.error('创建商品错误:', error);
    response.serverError(res, '创建商品失败');
  }
});

// 更新商品信息
router.put('/rewards/items/:id', validateId, requirePermission('admin'), uploadSingle('image'), async (req, res) => {
  try {
    const itemId = parseInt(req.params.id);
    const { name, description, category_id, points_cost, stock_quantity, status } = req.body;

    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (category_id !== undefined) updateData.category_id = category_id;
    if (points_cost !== undefined) updateData.points_cost = parseInt(points_cost);
    if (stock_quantity !== undefined) updateData.stock_quantity = parseInt(stock_quantity);
    if (status !== undefined) updateData.status = status;
    if (req.file) updateData.image_url = req.file.url;

    if (Object.keys(updateData).length === 0) {
      return response.validationError(res, null, '没有有效的更新字段');
    }

    const success = await RewardItem.update(itemId, updateData);

    if (success) {
      // 记录操作日志
      await Admin.logOperation(
        req.currentAdmin.id,
        'update_item',
        `更新商品：ID ${itemId}`
      );

      response.success(res, null, '商品信息更新成功');
    } else {
      response.notFound(res, '商品不存在');
    }

  } catch (error) {
    console.error('更新商品信息错误:', error);
    response.serverError(res, '更新商品信息失败');
  }
});

// 删除商品（软删除）
router.delete('/rewards/items/:id', validateId, requirePermission('admin'), async (req, res) => {
  try {
    const itemId = parseInt(req.params.id);

    const success = await RewardItem.softDelete(itemId);

    if (success) {
      // 记录操作日志
      await Admin.logOperation(
        req.currentAdmin.id,
        'delete_item',
        `删除商品：ID ${itemId}`
      );

      response.success(res, null, '商品删除成功');
    } else {
      response.notFound(res, '商品不存在');
    }

  } catch (error) {
    console.error('删除商品错误:', error);
    response.serverError(res, '删除商品失败');
  }
});

module.exports = router;
